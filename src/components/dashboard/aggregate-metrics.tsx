"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { PlatformAnalytics } from '@/lib/analytics-utils';
import { formatCurrency } from '@/lib/analytics-utils';

// Helper function to get today's metrics from analytics data
function getTodayMetrics(dataPoints: any[]) {
  if (!dataPoints || dataPoints.length === 0) return { gmv: 0, aov: 0, transactions: 0 };
  
  const today = new Date().toISOString().split('T')[0];
  const todayPoint = dataPoints.find(point => point.date === today);
  
  if (!todayPoint) return { gmv: 0, aov: 0, transactions: 0 };
  
  return {
    gmv: todayPoint.gmv,
    aov: todayPoint.aov,
    transactions: todayPoint.transactionCount
  };
}

// Helper function to get yesterday's metrics from analytics data
function getYesterdayMetrics(dataPoints: any[]) {
  if (!dataPoints || dataPoints.length === 0) return { gmv: 0, aov: 0, transactions: 0 };
  
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = yesterday.toISOString().split('T')[0];
  
  const yesterdayPoint = dataPoints.find(point => point.date === yesterdayStr);
  
  if (!yesterdayPoint) return { gmv: 0, aov: 0, transactions: 0 };
  
  return {
    gmv: yesterdayPoint.gmv,
    aov: yesterdayPoint.aov,
    transactions: yesterdayPoint.transactionCount
  };
}

interface AggregateMetricsProps {
  shopMyData?: PlatformAnalytics;
  strackrData?: PlatformAnalytics;
  loading?: boolean;
  error?: string;
}

export function AggregateMetrics({
  shopMyData,
  strackrData,
  loading,
  error
}: AggregateMetricsProps) {

  const todayTotalGMV = React.useMemo(() => {
    const shopMyToday = shopMyData?.dataPoints ? getTodayMetrics(shopMyData.dataPoints) : { gmv: 0 };
    const strackrToday = strackrData?.dataPoints ? getTodayMetrics(strackrData.dataPoints) : { gmv: 0 };
    return shopMyToday.gmv + strackrToday.gmv;
  }, [shopMyData, strackrData]);

  const yesterdayTotalGMV = React.useMemo(() => {
    const shopMyYesterday = shopMyData?.dataPoints ? getYesterdayMetrics(shopMyData.dataPoints) : { gmv: 0 };
    const strackrYesterday = strackrData?.dataPoints ? getYesterdayMetrics(strackrData.dataPoints) : { gmv: 0 };
    return shopMyYesterday.gmv + strackrYesterday.gmv;
  }, [shopMyData, strackrData]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {Array.from({ length: 2 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <div className="text-sm text-destructive">Error loading aggregate metrics: {error}</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Combined Platform Stats</h3>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* Today's Total GMV */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total GMV - Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(todayTotalGMV)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Shop My + Strackr combined
            </p>
          </CardContent>
        </Card>

        {/* Yesterday's Total GMV */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total GMV - Yesterday
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(yesterdayTotalGMV)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Shop My + Strackr combined
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
